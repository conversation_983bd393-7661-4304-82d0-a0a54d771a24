/**
 * 管理员LangChain智能体服务
 * 提供智能体管理、用户分配、对话测试等功能
 */

import apiClient from './apiClient'

/**
 * 管理员LangChain服务类
 */
class AdminLangChainService {

  // ==================== 私有工具方法 ====================

  /**
   * 统一错误处理方法
   * @param {Error} error - 错误对象
   * @param {string} operation - 操作名称
   * @param {string} format - 返回格式 ('standard' | 'success')
   * @returns {Object} 统一的错误响应
   */
  _handleError(error, operation, format = 'standard') {
    console.error(`${operation}失败:`, error)

    if (format === 'success') {
      return {
        success: false,
        error: error.userFriendlyMessage || error.message || `${operation}失败`
      }
    }

    return {
      status: 500,
      message: error.userFriendlyMessage || error.message || `${operation}失败`,
      data: null
    }
  }

  // ==================== 智能体基础管理 ====================

  /**
   * 管理员创建智能体
   * @param {Object} 智能体数据 - 智能体配置数据
   * @returns {Promise} API响应
   */
  async 管理员创建智能体(智能体数据) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/create', 智能体数据)
      return response
    } catch (error) {
      return this._handleError(error, '管理员创建智能体')
    }
  }

  /**
   * 获取所有智能体列表（管理员视图）
   * @param {Object} 查询参数 - 分页和过滤参数
   * @returns {Promise} API响应
   */
  async 获取所有智能体列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        智能体类型: null,
        状态: null,
        搜索关键词: null,
        是否公开: null
      }

      const 最终参数 = { ...默认参数, ...查询参数 }

      console.log('🔧 发送智能体列表请求:', 最终参数)

      const response = await apiClient.post('/admin/langchain/agents/list', 最终参数)

      console.log('📡 智能体列表API响应:', response)

      // 直接返回完整响应，包含status、data、message
      return response

    } catch (error) {
      console.error('获取智能体列表失败:', error)

      // 返回统一的错误格式
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取智能体列表失败',
        data: null
      }
    }
  }

  /**
   * 获取智能体详情
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取智能体详情(智能体id) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/detail', { 智能体id })
      return response
    } catch (error) {
      return this._handleError(error, '获取智能体详情')
    }
  }

  /**
   * 更新智能体
   * @param {number} 智能体id - 智能体id
   * @param {Object} 智能体数据 - 更新的智能体配置
   * @returns {Promise} API响应
   */
  async 更新智能体(智能体id, 智能体数据) {
    try {
      const 请求数据 = { 智能体id, ...智能体数据 }
      const response = await apiClient.post('/admin/langchain/agents/update', 请求数据)
      return response
    } catch (error) {
      return this._handleError(error, '更新智能体')
    }
  }

  /**
   * 删除智能体
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 删除智能体(智能体id) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/delete', {
        智能体id: 智能体id
      })
      return response
    } catch (error) {
      console.error('删除智能体失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '删除智能体失败',
        data: null
      }
    }
  }

  /**
   * 重新加载智能体配置
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 重新加载智能体配置(智能体id) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/reload', {
        智能体id: 智能体id
      })
      return response
    } catch (error) {
      console.error('重新加载智能体配置失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '重新加载智能体配置失败',
        data: null
      }
    }
  }

  /**
   * 重新加载所有智能体
   * @returns {Promise} API响应
   */
  async 重新加载所有智能体() {
    try {
      const response = await apiClient.post('/admin/langchain/system/reload')
      return response
    } catch (error) {
      console.error('重新加载所有智能体失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '重新加载所有智能体失败',
        data: null
      }
    }
  }

  // ==================== 用户分配管理 ====================

  /**
   * 分配智能体给用户
   * @param {Object} 分配参数 - 分配参数
   * @param {number} 分配参数.智能体id - 智能体id
   * @param {Object} 分配参数.分配数据 - 分配数据
   * @returns {Promise} API响应
   */
  async 分配智能体给用户({ 智能体id, 分配数据 }) {
    try {
      const 请求数据 = {
        智能体id: 智能体id,
        ...分配数据
      }
      const response = await apiClient.post('/admin/langchain/agents/assign', 请求数据)
      return response
    } catch (error) {
      console.error('分配智能体给用户失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '分配智能体给用户失败',
        data: null
      }
    }
  }

  /**
   * 获取用户智能体分配列表
   * @param {Object} 查询参数 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取用户智能体分配列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        用户id: null,
        智能体id: null,
        状态: null
      }

      const 最终参数 = { ...默认参数, ...查询参数 }
      const response = await apiClient.post('/admin/langchain/assignments/list', 最终参数)
      return response
    } catch (error) {
      console.error('获取用户智能体分配列表失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取用户智能体分配列表失败',
        data: null
      }
    }
  }

  /**
   * 撤销用户智能体分配
   * @param {number} 分配id - 分配ID
   * @returns {Promise} API响应
   */
  async 撤销用户智能体分配(分配id) {
    try {
      const response = await apiClient.post(`/admin/langchain/assignments/${分配id}/revoke`)
      return response
    } catch (error) {
      console.error('撤销用户智能体分配失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '撤销用户智能体分配失败',
        data: null
      }
    }
  }

  // ==================== 统计和监控 ====================

  /**
   * 获取智能体统计信息
   * @returns {Promise} API响应
   */
  async 获取智能体统计信息() {
    try {
      const response = await apiClient.post('/admin/langchain/statistics')
      return response
    } catch (error) {
      console.error('获取统计信息失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取统计信息失败',
        data: null
      }
    }
  }

  /**
   * 获取智能体工厂调试信息
   * @returns {Promise} API响应
   */
  async 获取智能体工厂调试信息() {
    try {
      const response = await apiClient.post('/admin/langchain/system/debug')
      return response
    } catch (error) {
      console.error('获取调试信息失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取调试信息失败',
        data: null
      }
    }
  }

  // ==================== 用户管理 ====================

  /**
   * 搜索用户
   * @param {Object} 搜索参数 - 搜索条件
   * @param {string} 搜索参数.关键词 - 搜索关键词
   * @param {number} 搜索参数.页码 - 页码，默认为1
   * @param {number} 搜索参数.每页数量 - 每页数量，默认为20
   * @returns {Promise} API响应
   */
  async 搜索用户(搜索参数) {
    try {
      const { 关键词, 页码 = 1, 每页数量 = 20 } = 搜索参数

      const response = await apiClient.post('/admin/langchain/users/search', {
        关键词,
        页码,
        每页数量
      })
      return response
    } catch (error) {
      console.error('搜索用户失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '搜索用户失败',
        data: null
      }
    }
  }

  // ==================== 对话测试 ====================

  /**
   * 测试智能体对话
   * @param {Object} 测试参数 - 测试对话参数
   * @param {number} 测试参数.智能体id - 智能体id
   * @param {string} 测试参数.用户消息 - 用户消息内容
   * @param {string} 测试参数.会话id - 会话ID
   * @param {boolean} 测试参数.测试模式 - 是否为测试模式
   * @param {Object} 测试参数.自定义变量 - 自定义变量
   * @returns {Promise} API响应
   */
  async 测试智能体对话({ 智能体id, 用户消息, 会话id, 测试模式 = true, 自定义变量 = null }) {
    try {
      const 请求数据 = {
        智能体id,
        用户消息,
        会话id,
        测试模式
      }

      // 只有当自定义变量存在且不为空时才添加
      if (自定义变量 && Object.keys(自定义变量).length > 0) {
        请求数据.自定义变量 = 自定义变量
        console.log('🔧 传递自定义变量到后端:', 自定义变量)
      }

      const response = await apiClient.post('/admin/langchain/agents/test', 请求数据)
      return response
    } catch (error) {
      console.error('测试智能体对话失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '测试智能体对话失败',
        data: null
      }
    }
  }

  /**
   * 获取智能体对话历史
   * @param {number} 智能体id - 智能体id
   * @param {Object} 查询参数 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取智能体对话历史(智能体id, 查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        会话id: null,
        用户id: null,
        开始时间: null,
        结束时间: null
      }

      const 最终参数 = {
        智能体id: 智能体id,
        ...默认参数,
        ...查询参数
      }
      const response = await apiClient.post('/admin/langchain/agents/conversations', 最终参数)
      return response
    } catch (error) {
      console.error('获取对话历史失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取对话历史失败',
        data: null
      }
    }
  }

  // ==================== 知识库管理 ====================

  /**
   * 获取所有知识库列表（管理员视图）
   * @param {Object} 查询参数 - 分页和过滤参数
   * @returns {Promise} API响应
   */
  async 获取所有知识库列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        状态: null,
        搜索关键词: null
      }

      const 最终参数 = { ...默认参数, ...查询参数 }
      const response = await apiClient.post('/admin/langchain/knowledge/list', 最终参数)

      console.log('📡 知识库API原始响应:', response)

      // 直接返回API响应，保持status字段
      return response
    } catch (error) {
      console.error('获取知识库列表失败:', error)
      // 返回标准错误格式，保持与成功响应一致的结构
      return {
        status: 500,
        message: error.response?.data?.message || error.message || '获取知识库列表失败',
        data: null
      }
    }
  }

  /**
   * 创建知识库（管理员）
   * @param {Object} 知识库数据 - 知识库配置
   * @returns {Promise} API响应
   */
  async 创建知识库(知识库数据) {
    try {
      const response = await apiClient.post('/admin/langchain/knowledge/create', 知识库数据)

      // 统一返回格式
      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message
        }
      } else {
        return {
          success: false,
          error: response.message || '创建知识库失败'
        }
      }
    } catch (error) {
      console.error('创建知识库失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message || '网络错误'
      }
    }
  }

  /**
   * 删除知识库
   * @param {number} 知识库id - 知识库ID
   * @returns {Promise} API响应
   */
  async 删除知识库(知识库id) {
    try {
      const response = await apiClient.post('/admin/langchain/knowledge/delete', {
        知识库id: 知识库id
      })
      return response
    } catch (error) {
      console.error('删除知识库失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '删除知识库失败',
        data: null
      }
    }
  }

  // ==================== PostgreSQL向量存储管理 ====================

  /**
   * 获取知识库PostgreSQL向量存储状态
   * @param {number} 知识库ID - 知识库ID
   * @returns {Promise} API响应
   */
  async 获取向量存储状态(知识库ID) {
    try {
      const response = await apiClient.post('/admin/langchain/knowledge/vector-status', {
        知识库ID: 知识库ID
      })

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message
        }
      } else {
        return {
          success: false,
          error: response.message || '获取PostgreSQL向量存储状态失败'
        }
      }

    } catch (error) {
      console.error('获取PostgreSQL向量存储状态失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message || '网络错误'
      }
    }
  }

  /**
   * 清空知识库向量数据
   * @param {number} 知识库ID - 知识库ID
   * @returns {Promise} API响应
   */
  async 清空知识库向量数据(知识库ID) {
    try {
      const response = await apiClient.post('/admin/langchain/knowledge/clear-vectors', {
        知识库ID: 知识库ID
      })

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message
        }
      } else {
        return {
          success: false,
          error: response.message || '清空知识库向量数据失败'
        }
      }

    } catch (error) {
      console.error('清空知识库向量数据失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message || '网络错误'
      }
    }
  }

  /**
   * 切换向量存储服务
   * @param {Object} data - 切换参数
   * @param {number} data.知识库ID - 知识库ID
   * @param {string} data.目标服务类型 - 目标服务类型 (postgresql/cloud)
   * @param {Object} data.云服务配置 - 云服务配置（可选）
   * @returns {Promise} API响应
   */
  async 切换向量存储服务(data) {
    try {
      const response = await apiClient.post('/admin/langchain/knowledge/switch-vector-service', {
        知识库ID: data.知识库ID,
        目标服务类型: data.目标服务类型,
        云服务配置: data.云服务配置 || {}
      })

      if (response.status === 100) {
        return {
          success: true,
          data: response.data,
          message: response.message
        }
      } else {
        return {
          success: false,
          error: response.message || '切换向量存储服务失败'
        }
      }

    } catch (error) {
      console.error('切换向量存储服务失败:', error)
      return {
        success: false,
        error: error.response?.data?.message || error.message || '网络错误'
      }
    }
  }

  // ==================== 系统管理 ====================

  /**
   * 获取系统配置
   * @returns {Promise} API响应
   */
  async 获取系统配置() {
    try {
      const response = await apiClient.post('/admin/langchain/system/config')
      return response.data
    } catch (error) {
      console.error('获取系统配置失败:', error)
      throw error
    }
  }

  /**
   * 更新系统配置
   * @param {Object} 配置数据 - 系统配置
   * @returns {Promise} API响应
   */
  async 更新系统配置(配置数据) {
    try {
      const response = await apiClient.post('/admin/langchain/system/config/update', 配置数据)
      return response.data
    } catch (error) {
      console.error('更新系统配置失败:', error)
      throw error
    }
  }

  /**
   * 获取系统日志
   * @param {Object} 查询参数 - 日志查询参数
   * @returns {Promise} API响应
   */
  async 获取系统日志(查询参数 = {}) {
    try {
      const response = await apiClient.post('/admin/langchain/system/logs', 查询参数)
      return response.data
    } catch (error) {
      console.error('获取系统日志失败:', error)
      throw error
    }
  }

  /**
   * 清理系统缓存
   * @returns {Promise} API响应
   */
  async 清理系统缓存() {
    try {
      const response = await apiClient.post('/admin/langchain/system/cache/clear')
      return response.data
    } catch (error) {
      console.error('清理系统缓存失败:', error)
      throw error
    }
  }

  /**
   * 导出系统数据
   * @param {Object} 导出参数 - 导出配置
   * @returns {Promise} API响应
   */
  async 导出系统数据(导出参数 = {}) {
    try {
      const response = await apiClient.post('/admin/langchain/system/export', 导出参数)
      return response.data
    } catch (error) {
      console.error('导出系统数据失败:', error)
      throw error
    }
  }

  /**
   * 导入系统数据
   * @param {FormData} 导入数据 - 包含文件的FormData
   * @returns {Promise} API响应
   */
  async 导入系统数据(导入数据) {
    try {
      const response = await apiClient.post('/admin/langchain/system/import', 导入数据, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('导入系统数据失败:', error)
      throw error
    }
  }

  // ==================== 新增的智能体编辑器相关方法 ====================







  /**
   * 获取智能体详情
   * @param {number} agentId - 智能体id
   * @returns {Promise} 智能体详情
   */
  async getAgentDetail(agentId) {
    try {
      console.log('🤖 智能体服务 - 获取智能体详情:', agentId)

      const response = await apiClient.post('/admin/langchain/agents/detail', { 智能体id: agentId })

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 获取智能体详情成功')
        return response.data
      } else {
        console.error('❌ 智能体服务 - 获取智能体详情失败:', response.message)
        throw new Error(response.message || '获取智能体详情失败')
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 获取智能体详情异常:', error)
      throw error
    }
  }

  /**
   * 创建智能体
   * @param {Object} agentData - 智能体数据
   * @returns {Promise} 创建结果
   */
  async createAgent(agentData) {
    try {
      console.log('🤖 智能体服务 - 创建智能体:', agentData)

      const response = await apiClient.post(`/admin/langchain/agents/create`, agentData)

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 创建智能体成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 创建智能体失败:', response.message)
        return {
          success: false,
          error: response.message || '创建智能体失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 创建智能体异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 更新智能体
   * @param {string|number} agentId - 智能体id
   * @param {Object} agentData - 智能体数据
   * @returns {Promise} 更新结果
   */
  async updateAgent(agentId, agentData) {
    try {
      console.log('🤖 智能体服务 - 更新智能体:', agentId)

      const response = await apiClient.post('/admin/langchain/agents/update', {
        智能体id: agentId,
        ...agentData
      })

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 更新智能体成功')
        return response.data
      } else {
        console.error('❌ 智能体服务 - 更新智能体失败:', response.message)
        // 根据业务状态码提供详细错误信息
        let errorMessage = response.message || '更新智能体失败'
        if (response.status === 1506) {
          errorMessage = '智能体不存在，请刷新页面重试'
        } else if (response.status === 1512) {
          errorMessage = '数据验证失败，请检查输入参数'
        } else if (response.status === 1502) {
          errorMessage = '智能体更新失败，请稍后重试'
        }
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 更新智能体异常:', error)
      throw error
    }
  }

  /**
   * 获取模型列表
   * @param {string} 模型类型 - 'chat' 或 'embedding'
   * @returns {Promise} 模型列表
   */
  async getModelList(模型类型 = null) {
    try {
      console.log(`🤖 智能体服务 - 获取模型列表 (类型: ${模型类型 || '全部'})`)

      if (模型类型 === 'embedding') {
        // 向量模型使用专门的接口
        const response = await apiClient.get('/admin/langchain/model-providers/embedding-models')

        if (response.status === 100) {
          console.log('✅ 智能体服务 - 获取向量模型列表成功')
          // 向量模型接口返回的数据结构：{ 模型列表: [...], 总数量: 10 }
          const 模型列表 = response.data?.模型列表 || []
          return {
            success: true,
            data: 模型列表
          }
        } else {
          console.error('❌ 智能体服务 - 获取向量模型列表失败:', response.message)
          return {
            success: false,
            error: response.message || '获取向量模型列表失败'
          }
        }
      } else {
        // 对话模型直接从数据库获取启用的模型
        const 请求数据 = {
          模型类型: null, // 获取所有类型的模型，后端会过滤出聊天类型的模型
          是否启用: true,
          页码: 1,
          每页数量: 100
        }

        console.log('📤 发送获取模型列表请求:', 请求数据)

        // 使用模型管理路由的接口
        const response = await apiClient.post('/admin/langchain/model-providers/models/list', 请求数据)

        console.log('📥 获取模型列表响应:', response)

        if (response.status === 100) {
          console.log('✅ 智能体服务 - 获取对话模型列表成功')

          // 检查返回数据结构
          let 模型列表 = []
          if (response.data) {
            // 如果是分页数据结构 {列表: [...], 总数: 10}
            if (response.data.列表) {
              模型列表 = response.data.列表
            }
            // 如果是直接数组
            else if (Array.isArray(response.data)) {
              模型列表 = response.data
            }
            // 如果是success格式 {success: true, data: [...]}
            else if (response.data.data && Array.isArray(response.data.data)) {
              模型列表 = response.data.data
            }
          }

          console.log(`📊 模型列表统计 - 总数: ${模型列表.length}`)
          console.log('📋 模型列表详情:', 模型列表.map(m => ({
            id: m.id,
            名称: m.显示名称 || m.模型名称,
            类型: m.模型类型,
            提供商: m.提供商
          })))

          return {
            success: true,
            data: 模型列表
          }
        } else {
          console.error('❌ 智能体服务 - 获取对话模型列表失败:', response.message)
          return {
            success: false,
            error: response.message || '获取对话模型列表失败'
          }
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 获取模型列表异常:', error)
      return {
        success: false,
        error: error.userFriendlyMessage || error.message || '网络请求失败'
      }
    }
  }



  /**
   * 获取可用嵌入模型列表
   * @returns {Promise} 嵌入模型列表
   */
  async 获取可用嵌入模型列表() {
    try {
      console.log('🤖 智能体服务 - 获取可用嵌入模型列表')

      const response = await apiClient.post('/admin/langchain/knowledge/embedding-models')

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 获取可用嵌入模型列表成功')
        return {
          status: 100,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 获取可用嵌入模型列表失败:', response.message)
        return {
          status: response.status,
          message: response.message || '获取可用嵌入模型列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 获取可用嵌入模型列表异常:', error)
      return {
        status: 500,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取可用AI模型列表
   * @returns {Promise} AI模型列表
   */
  async 获取可用AI模型列表() {
    try {
      console.log('🤖 智能体服务 - 获取可用AI模型列表')

      const response = await apiClient.post('/admin/langchain/knowledge/ai-models')

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 获取可用AI模型列表成功')
        return {
          status: 100,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 获取可用AI模型列表失败:', response.message)
        return {
          status: response.status,
          message: response.message || '获取可用AI模型列表失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 获取可用AI模型列表异常:', error)
      return {
        status: 500,
        message: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 测试智能体检索
   * @param {number} agentId - 智能体id
   * @param {Object} testData - 测试数据
   * @returns {Promise} 检索测试结果
   */
  async testAgentRetrieval(agentId, testData) {
    try {
      console.log('🤖 智能体服务 - 测试智能体检索:', agentId, testData)

      const 请求数据 = { 智能体id: agentId, ...testData }
      const response = await apiClient.post('/admin/langchain/agent/test_retrieval', 请求数据)

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 测试智能体检索成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 测试智能体检索失败:', response.message)
        return {
          success: false,
          error: response.message || '测试智能体检索失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 测试智能体检索异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 验证用户智能体权限
   * @param {number} 用户id - 用户id
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} 权限验证结果
   */
  async 验证用户智能体权限(用户id, 智能体id) {
    try {
      console.log('🔍 智能体服务 - 验证用户权限:', { 用户id, 智能体id })

      const response = await apiClient.post('/admin/langchain/users/verify-agent-permission', {
        用户id,
        智能体id
      })

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 用户权限验证成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 用户权限验证失败:', response.message)
        return {
          success: false,
          error: response.message || '用户权限验证失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 用户权限验证异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 与智能体对话
   * @param {number} agentId - 智能体id
   * @param {Object} chatData - 对话数据
   * @returns {Promise} 对话结果
   */
  async chatWithAgent(agentId, chatData) {
    try {
      console.log('🤖 智能体服务 - 与智能体对话:', agentId, chatData)

      const 请求数据 = {
        智能体id: agentId,
        用户消息: chatData.消息,
        会话id: chatData.会话id || null,
        测试模式: true,
        自定义变量: chatData.自定义变量 || null
      }

      const response = await apiClient.post('/admin/langchain/agents/test', 请求数据)

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 与智能体对话成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 与智能体对话失败:', response.message)
        return {
          success: false,
          error: response.message || '与智能体对话失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 与智能体对话异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  /**
   * 获取智能体关联的知识库
   * @param {number} agentId - 智能体id
   * @returns {Promise} 关联知识库列表
   */
  async getAgentKnowledgeBases(agentId) {
    try {
      console.log('🤖 智能体服务 - 获取智能体关联知识库:', agentId)

      const response = await apiClient.post('/admin/langchain/agent/knowledge_bases', { 智能体id: agentId })

      if (response.status === 100) {
        console.log('✅ 智能体服务 - 获取智能体关联知识库成功')
        return {
          success: true,
          data: response.data
        }
      } else {
        console.error('❌ 智能体服务 - 获取智能体关联知识库失败:', response.message)
        return {
          success: false,
          error: response.message || '获取智能体关联知识库失败'
        }
      }
    } catch (error) {
      console.error('❌ 智能体服务 - 获取智能体关联知识库异常:', error)
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  // ==================== 结构化输出管理 ====================

  // ==================== 结构化输出相关 ====================

  /**
   * 验证Pydantic模型定义 - Context7优化版本
   * @param {string} 模型定义 - Pydantic模型代码
   * @returns {Promise} API响应
   */
  async 验证Pydantic模型定义(模型定义) {
    try {
      console.log('🔧 发送Pydantic验证请求:', { 模型定义 })

      const response = await apiClient.post('/admin/langchain/validate-pydantic-model', {
        模型定义: 模型定义,
        模式: 'with_structured_output'
      })

      console.log('📡 Pydantic验证API响应:', response)

      return response

    } catch (error) {
      console.error('Pydantic模型验证失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || 'Pydantic模型验证失败',
        data: null
      }
    }
  }

  // Pydantic模板示例功能已移除，使用可视化JSON设计器



  /**
   * 获取智能体可用知识库列表
   * @param {number} 智能体id - 智能体id
   * @returns {Promise<Object>} 知识库列表
   */
  async 获取智能体可用知识库列表(智能体id) {
    try {
      console.log('📚 智能体服务 - 获取可用知识库:', 智能体id)

      const response = await apiClient.post('/admin/langchain/agent/knowledge_bases', {
        智能体id: 智能体id
      })

      console.log('📡 可用知识库API响应:', response)
      return response

    } catch (error) {
      console.error('❌ 获取可用知识库失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取可用知识库失败',
        data: null
      }
    }
  }



  // ==================== 结构化输出测试相关 ====================

  /**
   * 测试智能体结构化输出
   * @param {Object} data - 测试参数
   * @param {number} data.智能体id - 智能体id
   * @param {string} data.测试消息 - 测试消息
   * @param {string} data.pydantic模型定义 - Pydantic模型代码
   * @returns {Promise<Object>} 测试结果
   */
  async 测试智能体结构化输出(data) {
    try {
      console.log('🔧 智能体服务 - 测试结构化输出:', data)

      const response = await apiClient.post('/admin/langchain/test-agent-structured-output', {
        智能体id: data.智能体id,
        测试消息: data.测试消息,
        pydantic模型定义: data.pydantic模型定义,
        输出格式: 'pydantic'
      })

      console.log('📡 结构化输出测试API响应:', response)
      return response

    } catch (error) {
      console.error('❌ 结构化输出测试失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '结构化输出测试失败',
        data: null
      }
    }
  }



  /**
   * 切换智能体启用状态
   * @param {number} 智能体id - 智能体id
   * @param {boolean} 是否启用 - 是否启用
   * @returns {Promise} API响应
   */
  async 切换智能体启用状态(智能体id, 是否启用) {
    try {
      console.log('🔄 切换智能体启用状态:', { 智能体id, 是否启用 })

      const response = await apiClient.put(`/admin/langchain/agents/${智能体id}/enable`, 是否启用)

      console.log('✅ 切换智能体启用状态成功:', response)
      return response
    } catch (error) {
      console.error('切换智能体启用状态失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '切换智能体启用状态失败',
        data: null
      }
    }
  }

  /**
   * 切换智能体RAG开关
   * @param {Object} data - RAG开关数据
   * @param {number} data.智能体id - 智能体id
   * @param {boolean} data.启用rag - 是否启用rag
   * @param {Array} data.知识库列表 - 关联的知识库ID列表（可选）
   * @returns {Promise} API响应
   */
  async 切换智能体RAG开关(data) {
    try {
      console.log('🔄 切换智能体RAG开关:', data)

      const 请求数据 = {
        智能体id: data.智能体id,
        启用rag: data.启用rag
      }

      // 如果关闭RAG，清空知识库列表
      if (!data.启用rag) {
        请求数据.知识库列表 = []
      } else if (data.知识库列表) {
        // 如果开启RAG且提供了知识库列表，则包含知识库列表
        请求数据.知识库列表 = data.知识库列表
      }

      const response = await apiClient.post('/admin/langchain/agents/update', 请求数据)

      if (response.status === 100) {
        console.log('✅ RAG开关切换成功:', response)
        return {
          status: 100,
          message: `RAG功能已${data.启用rag ? '开启' : '关闭'}`,
          data: response.data
        }
      } else {
        console.error('❌ RAG开关切换失败:', response.message)
        return {
          status: response.status || 500,
          message: response.message || 'RAG开关切换失败',
          data: null
        }
      }
    } catch (error) {
      console.error('❌ RAG开关切换异常:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || 'RAG开关切换失败',
        data: null
      }
    }
  }

  // ==================== 内部函数工具管理 ====================

  /**
   * 获取内部函数工具配置列表
   * @param {string} 工具分类 - 工具分类筛选
   * @param {boolean} 是否启用 - 启用状态筛选
   * @returns {Promise} API响应
   */
  async 获取内部函数工具配置列表(工具分类 = null, 是否启用 = null) {
    try {
      const response = await apiClient.post('/admin/internal-function-tools/config/list', {
        工具分类,
        是否启用
      })
      return response
    } catch (error) {
      console.error('获取内部函数工具配置列表失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取工具配置列表失败',
        data: null
      }
    }
  }

  /**
   * 保存内部函数工具配置
   * @param {Object} 工具配置 - 工具配置数据
   * @returns {Promise} API响应
   */
  async 保存内部函数工具配置(工具配置) {
    try {
      const response = await apiClient.post('/admin/internal-function-tools/config/save', 工具配置)
      return response
    } catch (error) {
      console.error('保存内部函数工具配置失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '保存工具配置失败',
        data: null
      }
    }
  }

  /**
   * 获取指定工具的配置
   * @param {string} 工具名称 - 工具名称
   * @returns {Promise} API响应
   */
  async 获取内部函数工具配置(工具名称) {
    try {
      const response = await apiClient.get(`/admin/internal-function-tools/config/${工具名称}`)
      return response
    } catch (error) {
      console.error('获取内部函数工具配置失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取工具配置失败',
        data: null
      }
    }
  }

  /**
   * 保存智能体工具关联配置 - 使用统一的工具关联API
   * @param {number} 智能体id - 智能体id
   * @param {Array} 工具列表 - 工具配置列表
   * @returns {Promise} API响应
   */
  async 保存智能体工具关联(智能体id, 工具列表) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/tools/save', {
        智能体id,
        工具列表
      })
      return {
        success: true,
        data: response.data,
        message: '保存智能体工具关联成功'
      }
    } catch (error) {
      console.error('保存智能体工具关联失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '保存智能体工具关联失败'
      }
    }
  }

  /**
   * 切换智能体工具开关 - 基于工具ID直接对接
   * @param {number} 智能体id - 智能体id
   * @param {number} 工具ID - 工具ID
   * @param {boolean} 启用状态 - 是否启用
   * @param {Object} 工具配置 - 工具配置参数
   * @returns {Promise} API响应
   */
  async 切换智能体工具开关(智能体id, 工具ID, 启用状态, 工具配置 = {}) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/tools/toggle', {
        智能体id,
        工具ID,
        启用状态,
        工具配置: {
          priority: 工具配置.priority || 5,
          description: 工具配置.description || ''
        }
      })
      return {
        success: true,
        data: response.data,
        message: `工具${启用状态 ? '启用' : '禁用'}成功`
      }
    } catch (error) {
      console.error('切换智能体工具开关失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '切换工具开关失败'
      }
    }
  }

  /**
   * 获取工具列表 - 获取所有可用的内部函数工具
   * @returns {Promise} API响应
   */
  async 获取工具列表() {
    try {
      const response = await apiClient.post('/admin/langchain/tools/list', {})
      return {
        success: true,
        data: response.data || [],
        message: '获取工具列表成功'
      }
    } catch (error) {
      console.error('获取工具列表失败:', error)
      return {
        success: false,
        data: [],
        message: error.userFriendlyMessage || error.message || '获取工具列表失败'
      }
    }
  }

  /**
   * 测试工具连接 - 测试内部函数工具系统连接
   * @returns {Promise} API响应
   */
  async 测试工具连接() {
    try {
      const response = await apiClient.post('/admin/langchain/tools/test-connection', {})
      return {
        success: true,
        data: response.data,
        message: '工具连接测试成功'
      }
    } catch (error) {
      console.error('测试工具连接失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '测试工具连接失败'
      }
    }
  }

  /**
   * 切换工具启用状态 - 切换单个工具的全局启用状态
   * @param {string} 工具名称 - 工具名称
   * @param {boolean} 启用状态 - 是否启用
   * @returns {Promise} API响应
   */
  async 切换工具启用状态(工具名称, 启用状态) {
    try {
      const response = await apiClient.post('/admin/langchain/tools/toggle', {
        工具名称,
        启用状态
      })
      return {
        success: true,
        data: response.data,
        message: `工具${工具名称}${启用状态 ? '启用' : '禁用'}成功`
      }
    } catch (error) {
      console.error('切换工具启用状态失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '切换工具启用状态失败'
      }
    }
  }

  /**
   * 获取智能体可用工具列表
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取智能体可用工具列表(智能体id) {
    try {
      const response = await apiClient.get(`/admin/internal-function-tools/agent-tools/${智能体id}`)
      return response
    } catch (error) {
      console.error('获取智能体可用工具列表失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取智能体可用工具列表失败',
        data: null
      }
    }
  }

  /**
   * 获取内部函数调用日志列表
   * @param {Object} 查询条件 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取内部函数调用日志列表(查询条件 = {}) {
    try {
      const 默认条件 = {
        用户id: null,
        智能体id: null,
        工具名称: null,
        页码: 1,
        每页数量: 20
      }

      const 最终条件 = { ...默认条件, ...查询条件 }
      const response = await apiClient.post('/admin/internal-function-tools/logs/list', 最终条件)
      return response
    } catch (error) {
      console.error('获取内部函数调用日志列表失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取调用日志列表失败',
        data: null
      }
    }
  }

  /**
   * 获取内部函数工具系统状态
   * @returns {Promise} API响应
   */
  async 获取内部函数工具系统状态() {
    try {
      const response = await apiClient.get('/admin/internal-function-tools/status')
      return response
    } catch (error) {
      console.error('获取内部函数工具系统状态失败:', error)
      return {
        status: 500,
        message: error.userFriendlyMessage || error.message || '获取系统状态失败',
        data: null
      }
    }
  }

  // ==================== 工具管理相关 ====================



  /**
   * 测试工具调用
   * @param {Object} 测试数据 - 包含工具名称和参数
   * @returns {Promise} API响应
   */
  async 测试工具调用(测试数据) {
    try {
      const response = await apiClient.post('/admin/langchain/tools/test', 测试数据)
      return {
        success: true,
        data: response.data,
        message: '工具测试成功'
      }
    } catch (error) {
      console.error('测试工具调用失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '工具测试失败'
      }
    }
  }

  /**
   * 获取智能体工具关联
   * @param {number} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取智能体工具关联(智能体id) {
    try {
      // 确保智能体id是纯数值，避免Vue响应式对象的循环引用
      const 纯数值ID = Number(智能体id)
      const response = await apiClient.post('/admin/langchain/agents/tools', { 智能体id: 纯数值ID })
      return {
        success: true,
        data: response.data || [],
        message: '获取工具关联成功'
      }
    } catch (error) {
      console.error('获取智能体工具关联失败:', error)
      return {
        success: false,
        data: [],
        message: error.userFriendlyMessage || error.message || '获取工具关联失败'
      }
    }
  }

  /**
   * 验证智能体工具调用
   * @param {Object} 验证数据 - 包含智能体id、工具名称、参数
   * @returns {Promise} API响应
   */
  async 验证智能体工具调用(验证数据) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/tools/verify', 验证数据)
      return {
        success: true,
        data: response.data,
        message: '工具调用验证成功'
      }
    } catch (error) {
      console.error('验证智能体工具调用失败:', error)
      return {
        success: false,
        data: null,
        message: error.userFriendlyMessage || error.message || '工具调用验证失败'
      }
    }
  }

  /**
   * 获取内部函数工具列表 - 已合并到获取可用工具列表接口
   * @deprecated 使用 获取可用工具列表() 替代
   * @returns {Promise} API响应
   */
  async 获取内部函数工具列表() {
    // 重定向到统一的工具列表接口
    return await this.获取可用工具列表()
  }

  /**
   * 获取可用工具列表 - 兼容性方法
   * @returns {Promise} API响应
   */
  async 获取可用工具列表() {
    try {
      const response = await apiClient.post('/admin/langchain/tools/list', {})
      return {
        success: true,
        data: response.data || [],
        message: '获取工具列表成功'
      }
    } catch (error) {
      console.error('获取可用工具列表失败:', error)
      return {
        success: false,
        data: [],
        message: error.userFriendlyMessage || error.message || '获取工具列表失败'
      }
    }
  }

  // ==================== 查询优化配置管理 ====================

  /**
   * 获取可用查询优化模型列表
   * @returns {Promise} API响应
   */
  async 获取可用查询优化模型列表() {
    try {
      const response = await apiClient.get('/admin/langchain/query-optimization/models')
      return {
        success: true,
        data: response.data || [],
        message: '获取查询优化模型列表成功'
      }
    } catch (error) {
      console.error('获取查询优化模型列表失败:', error)
      return {
        success: false,
        data: [],
        message: error.userFriendlyMessage || error.message || '获取查询优化模型列表失败'
      }
    }
  }

  /**
   * 获取智能体查询优化配置
   * @param {number} 智能体id - 智能体id
   * @param {number} 知识库ID - 知识库ID（可选）
   * @returns {Promise} API响应
   */
  async 获取智能体查询优化配置(智能体id, 知识库ID = null) {
    try {
      const params = 知识库ID ? { knowledge_id: 知识库ID } : {}
      const response = await apiClient.get(`/admin/langchain/agents/${智能体id}/query-optimization`, { params })
      return {
        success: true,
        data: response.data || {},
        message: '获取智能体查询优化配置成功'
      }
    } catch (error) {
      console.error('获取智能体查询优化配置失败:', error)
      return {
        success: false,
        data: {},
        message: error.userFriendlyMessage || error.message || '获取智能体查询优化配置失败'
      }
    }
  }

  /**
   * 更新智能体查询优化配置
   * @param {Object} 配置数据 - 查询优化配置数据
   * @returns {Promise} API响应
   */
  async 更新智能体查询优化配置(配置数据) {
    try {
      const response = await apiClient.put('/admin/langchain/agents/query-optimization', 配置数据)
      return {
        success: true,
        data: response.data || {},
        message: '更新智能体查询优化配置成功'
      }
    } catch (error) {
      console.error('更新智能体查询优化配置失败:', error)
      return {
        success: false,
        data: {},
        message: error.userFriendlyMessage || error.message || '更新智能体查询优化配置失败'
      }
    }
  }

  /**
   * 获取知识库查询优化配置
   * @param {number} 知识库ID - 知识库ID
   * @returns {Promise} API响应
   */
  async 获取知识库查询优化配置(知识库ID) {
    try {
      const response = await apiClient.get(`/admin/langchain/knowledge/${知识库ID}/query-optimization`)
      return {
        success: true,
        data: response.data || {},
        message: '获取知识库查询优化配置成功'
      }
    } catch (error) {
      console.error('获取知识库查询优化配置失败:', error)
      return {
        success: false,
        data: {},
        message: error.userFriendlyMessage || error.message || '获取知识库查询优化配置失败'
      }
    }
  }

  /**
   * 更新知识库查询优化配置
   * @param {number} 知识库ID - 知识库ID
   * @param {Object} 配置数据 - 查询优化配置数据
   * @returns {Promise} API响应
   */
  async 更新知识库查询优化配置(知识库ID, 配置数据) {
    try {
      const response = await apiClient.put(`/admin/langchain/knowledge/${知识库ID}/query-optimization`, 配置数据)
      return {
        success: true,
        data: response.data || {},
        message: '更新知识库查询优化配置成功'
      }
    } catch (error) {
      console.error('更新知识库查询优化配置失败:', error)
      return {
        success: false,
        data: {},
        message: error.userFriendlyMessage || error.message || '更新知识库查询优化配置失败'
      }
    }
  }

  /**
   * 测试查询优化效果
   * @param {Object} 测试数据 - 查询优化测试数据
   * @returns {Promise} API响应
   */
  async 测试查询优化效果(测试数据) {
    try {
      const response = await apiClient.post('/admin/langchain/query-optimization/test', 测试数据)
      return {
        success: true,
        data: response.data || {},
        message: '查询优化效果测试成功'
      }
    } catch (error) {
      console.error('测试查询优化效果失败:', error)
      return {
        success: false,
        data: {},
        message: error.userFriendlyMessage || error.message || '查询优化效果测试失败'
      }
    }
  }

}

// 创建并导出服务实例
export const adminLangChainService = new AdminLangChainService()
export default adminLangChainService
