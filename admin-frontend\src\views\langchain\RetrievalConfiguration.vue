<template>
  <div class="retrieval-configuration">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-page-header
        :title="`检索配置 - ${知识库名称}`"
        @back="返回知识库管理"
      >
        <template #extra>
          <a-space>
            <a-button @click="测试检索" :loading="测试中">
              <template #icon>
                <SearchOutlined />
              </template>
              测试检索
            </a-button>
            <a-button type="primary" @click="保存配置" :loading="保存中">
              <template #icon>
                <SaveOutlined />
              </template>
              保存配置
            </a-button>
          </a-space>
        </template>
      </a-page-header>
    </div>

    <!-- 配置表单 -->
    <div class="config-content">
      <a-card title="检索策略配置">
        <a-form
          :model="检索配置"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-form-item label="检索策略">
            <a-select v-model:value="检索配置.检索策略" placeholder="选择检索策略">
              <a-select-option value="vector">向量检索</a-select-option>
              <a-select-option value="keyword">关键词检索</a-select-option>
              <a-select-option value="hybrid">混合检索</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="相似度阈值">
            <a-slider
              v-model:value="检索配置.相似度阈值"
              :min="0"
              :max="1"
              :step="0.01"
              :marks="{ 0: '0', 0.5: '0.5', 1: '1' }"
            />
            <span class="threshold-value">{{ 检索配置.相似度阈值 }}</span>
          </a-form-item>

          <a-form-item label="最大检索数量">
            <a-input-number
              v-model:value="检索配置.最大检索数量"
              :min="1"
              :max="100"
              placeholder="输入最大检索数量"
            />
          </a-form-item>

          <a-form-item label="分块大小">
            <a-input-number
              v-model:value="检索配置.分块大小"
              :min="100"
              :max="5000"
              placeholder="输入分块大小"
            />
          </a-form-item>

          <a-form-item label="分块重叠">
            <a-input-number
              v-model:value="检索配置.分块重叠"
              :min="0"
              :max="1000"
              placeholder="输入分块重叠大小"
            />
          </a-form-item>

          <a-form-item label="分块策略">
            <a-select v-model:value="检索配置.分块策略" placeholder="选择分块策略">
              <a-select-option value="recursive">递归分块</a-select-option>
              <a-select-option value="semantic">语义分块</a-select-option>
              <a-select-option value="fixed">固定大小分块</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="嵌入模型">
            <a-select
              v-model:value="检索配置.嵌入模型id"
              placeholder="选择嵌入模型"
              :loading="嵌入模型加载中"
              @focus="加载嵌入模型列表"
              :not-found-content="嵌入模型加载中 ? '加载中...' : '暂无可用模型'"
            >
              <a-select-option
                v-for="model in 嵌入模型列表"
                :key="model.id"
                :value="model.id"
                :disabled="model.状态 !== 'active'"
              >
                <div class="model-option">
                  <span class="model-name">{{ model.模型名称 }}</span>
                  <a-tag
                    :color="model.状态 === 'active' ? 'green' : 'orange'"
                    size="small"
                  >
                    {{ model.状态 === 'active' ? '可用' : '不可用' }}
                  </a-tag>
                </div>
              </a-select-option>
            </a-select>
            <div class="help-text">
              <small>选择用于文档向量化和检索的嵌入模型，建议选择与知识库创建时相同的模型</small>
            </div>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 测试检索区域 -->
      <a-card title="检索测试" class="test-section">
        <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="测试查询">
            <a-textarea
              v-model:value="测试查询文本"
              placeholder="输入测试查询文本"
              :rows="3"
            />
          </a-form-item>
          <a-form-item>
            <a-button @click="执行测试检索" :loading="测试中" type="primary">
              执行检索测试
            </a-button>
          </a-form-item>
        </a-form>

        <!-- 测试结果 -->
        <div v-if="测试结果.length > 0" class="test-results">
          <h4>检索结果</h4>
          <a-list
            :data-source="测试结果"
            item-layout="vertical"
            size="small"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <template #extra>
                  <a-tag color="blue">相似度: {{ (item.相似度分数 || item.相似度 || 0).toFixed(3) }}</a-tag>
                </template>
                <a-list-item-meta>
                  <template #title>
                    <span>结果 {{ index + 1 }}: {{ item.文档名称 || '未知来源' }}</span>
                  </template>
                  <template #description>
                    <div class="result-content">{{ item.分块内容 || item.内容 || '内容不可用' }}</div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SearchOutlined, SaveOutlined } from '@ant-design/icons-vue'
import knowledgeBaseService from '@/services/knowledgeBaseService'

// 组件名称设置
defineOptions({
  name: 'cn-检索配置'
})

// 路由相关
const route = useRoute()
const router = useRouter()

// 服务实例已通过import导入

// 响应式数据
const 知识库ID = ref(parseInt(route.params.knowledgeBaseId))
const 知识库名称 = ref(route.query.knowledgeBaseName || '未知知识库')
const 保存中 = ref(false)
const 测试中 = ref(false)
const 测试查询文本 = ref('')
const 测试结果 = ref([])
const 嵌入模型列表 = ref([])
const 嵌入模型加载中 = ref(false)

// 检索配置数据
const 检索配置 = reactive({
  检索策略: 'vector',
  相似度阈值: 0.7,
  最大检索数量: 10,
  分块大小: 1000,
  分块重叠: 200,
  分块策略: 'recursive',
  嵌入模型id: null
})

/**
 * 加载嵌入模型列表
 */
const 加载嵌入模型列表 = async () => {
  if (嵌入模型列表.value.length > 0) {
    return // 已经加载过了
  }

  try {
    嵌入模型加载中.value = true
    console.log('🤖 加载嵌入模型列表')

    const response = await knowledgeBaseService.getVectorModels()

    if (response.success) {
      嵌入模型列表.value = response.data.模型列表 || []
      console.log('✅ 嵌入模型列表加载成功:', 嵌入模型列表.value.length)

      // 如果检索配置中没有选择嵌入模型，自动选择第一个可用的
      if (!检索配置.嵌入模型id && 嵌入模型列表.value.length > 0) {
        const 默认模型 = 嵌入模型列表.value.find(model => model.状态 === 'active')
        if (默认模型) {
          检索配置.嵌入模型id = 默认模型.id
          console.log('🎯 自动选择默认嵌入模型:', 默认模型.模型名称)
        }
      }
    } else {
      console.error('❌ 加载嵌入模型列表失败:', response.error)
      message.error('加载嵌入模型列表失败')
    }
  } catch (error) {
    console.error('❌ 加载嵌入模型列表异常:', error)
    message.error('加载嵌入模型列表失败')
  } finally {
    嵌入模型加载中.value = false
  }
}

/**
 * 加载检索配置
 */
const 加载检索配置 = async () => {
  try {
    console.log('🔧 加载检索配置:', 知识库ID.value)

    const response = await knowledgeBaseService.getRetrievalConfig(知识库ID.value)

    if (response.success) {
      Object.assign(检索配置, response.data)
      console.log('✅ 检索配置加载成功')
    } else {
      console.warn('⚠️ 使用默认检索配置')
    }
  } catch (error) {
    console.error('❌ 加载检索配置失败:', error)
    message.error('加载检索配置失败')
  }
}

/**
 * 保存配置
 */
const 保存配置 = async () => {
  try {
    保存中.value = true
    console.log('💾 保存检索配置:', 检索配置)
    
    const response = await knowledgeBaseService.updateRetrievalConfig(知识库ID.value, 检索配置)
    
    if (response.success) {
      message.success('检索配置保存成功')
      console.log('✅ 检索配置保存成功')
    } else {
      message.error(response.error || '保存检索配置失败')
    }
  } catch (error) {
    console.error('❌ 保存检索配置失败:', error)
    message.error('保存检索配置失败')
  } finally {
    保存中.value = false
  }
}

/**
 * 测试检索
 */
const 测试检索 = () => {
  if (!测试查询文本.value.trim()) {
    message.warning('请输入测试查询文本')
    return
  }
  执行测试检索()
}

/**
 * 执行测试检索（使用新版本接口）
 */
const 执行测试检索 = async () => {
  try {
    测试中.value = true
    console.log('🔍 执行检索测试（新版本）:', 测试查询文本.value)

    // 检查是否选择了嵌入模型
    if (!检索配置.嵌入模型id) {
      message.warning('请先选择嵌入模型')
      return
    }

    // 获取嵌入模型名称
    const 选中的嵌入模型 = 嵌入模型列表.value.find(model => model.id === 检索配置.嵌入模型id)
    if (!选中的嵌入模型) {
      message.error('无法找到选中的嵌入模型信息')
      return
    }

    // 构建检索配置参数（使用实时页面参数）
    const 检索配置参数 = {
      检索策略: 检索配置.检索策略 || 'vector',
      嵌入模型: 选中的嵌入模型.模型名称,
      相似度阈值: 检索配置.相似度阈值,
      最大检索数量: 检索配置.最大检索数量,
      分块大小: 检索配置.分块大小 || 1000,
      分块重叠: 检索配置.分块重叠 || 200,
      分块策略: 检索配置.分块策略 || 'recursive'
    }

    // 构建查询优化配置（如果启用）
    let 查询优化配置 = null
    if (检索配置.启用查询优化) {
      查询优化配置 = {
        启用查询优化: true,
        优化模型: 检索配置.查询优化模型,
        优化提示词: 检索配置.查询优化提示词
      }
    }

    console.log('📊 使用实时参数进行检索测试:', {
      知识库列表: [知识库ID.value],
      检索配置参数,
      查询优化配置
    })

    // 使用新版本接口进行检索测试
    const response = await knowledgeBaseService.testRetrievalNew(
      [知识库ID.value], // 知识库ID列表
      测试查询文本.value, // 测试查询
      检索配置参数, // 检索配置
      查询优化配置, // 查询优化配置
      'standard' // 测试模式
    )

    if (response.success) {
      测试结果.value = response.data.检索结果 || []
      const 总结果数量 = response.data.总结果数量 || 测试结果.value.length
      message.success(`检索完成，找到 ${总结果数量} 个相关结果`)
      console.log('✅ 检索测试成功（新版本）:', response.data)
    } else {
      message.error(response.error || '检索测试失败')
      测试结果.value = []
    }
  } catch (error) {
    console.error('❌ 检索测试失败（新版本）:', error)
    message.error('检索测试失败: ' + error.message)
    测试结果.value = []
  } finally {
    测试中.value = false
  }
}

/**
 * 返回知识库管理
 */
const 返回知识库管理 = () => {
  router.push('/langchain/knowledge-base')
}

// 组件挂载时加载数据
onMounted(async () => {
  console.log('🎯 检索配置页面已挂载')
  await Promise.all([
    加载检索配置(),
    加载嵌入模型列表()
  ])
})
</script>

<style scoped>
.retrieval-configuration {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.config-content {
  max-width: 800px;
}

.test-section {
  margin-top: 24px;
}

.threshold-value {
  margin-left: 16px;
  font-weight: bold;
  color: #1890ff;
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.model-name {
  flex: 1;
  margin-right: 8px;
}

.help-text {
  margin-top: 4px;
  color: #666;
}

.help-text small {
  font-size: 12px;
  line-height: 1.4;
}

.test-results {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.result-content {
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  margin-top: 8px;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-word;
}
</style> 