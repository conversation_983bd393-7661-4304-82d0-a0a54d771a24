<template>
  <div class="rag-test-panel">
    <div class="test-header">
      <h3>RAG检索测试</h3>
      <p>测试知识库检索效果</p>
    </div>

    <a-form layout="vertical" class="test-form">
      <!-- 测试查询 -->
      <a-form-item label="测试查询">
        <a-textarea
          v-model:value="testQuery"
          placeholder="输入要测试的查询内容"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>

      <!-- 测试参数 -->
      <a-collapse ghost>
        <a-collapse-panel key="params" header="测试参数">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="检索数量">
                <a-input-number
                  v-model:value="testParams.最大检索数量"
                  :min="1"
                  :max="20"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="相似度阈值">
                <a-input-number
                  v-model:value="testParams.相似度阈值"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="检索策略">
            <a-select v-model:value="testParams.检索策略">
              <a-select-option value="vector">向量检索</a-select-option>
              <a-select-option value="keyword">关键词匹配</a-select-option>
              <a-select-option value="mixed">混合检索</a-select-option>
            </a-select>
          </a-form-item>
        </a-collapse-panel>
      </a-collapse>

      <!-- 测试按钮 -->
      <a-form-item>
        <a-space>
          <a-button 
            type="primary" 
            @click="runTest"
            :loading="testing"
            :disabled="!testQuery.trim() || knowledgeBaseIds.length === 0"
          >
            <SearchOutlined /> 开始测试
          </a-button>
          <a-button @click="clearResults">
            <ClearOutlined /> 清空结果
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>

    <!-- 测试结果 -->
    <div v-if="testResults.length > 0" class="test-results">
      <div class="results-header">
        <h4>检索结果 ({{ testResults.length }} 条)</h4>
        <a-space>
          <span class="result-stats">
            平均相似度: {{ averageScore.toFixed(3) }}
          </span>
          <a-button size="small" @click="exportResults">
            <DownloadOutlined /> 导出结果
          </a-button>
        </a-space>
      </div>

      <div class="results-list">
        <div 
          v-for="(result, index) in testResults" 
          :key="index"
          class="result-item"
        >
          <div class="result-header">
            <div class="result-index">#{{ index + 1 }}</div>
            <div class="result-score">
              <a-progress 
                :percent="Math.round(result.相似度分数 * 100)" 
                size="small"
                :stroke-color="getScoreColor(result.相似度分数)"
              />
              <span class="score-text">{{ result.相似度分数.toFixed(3) }}</span>
            </div>
          </div>

          <div class="result-content">
            <div class="result-text">{{ result.文档内容 }}</div>
            <div class="result-meta">
              <a-space size="small">
                <a-tag size="small" color="blue">
                  {{ result.知识库名称 }}
                </a-tag>
                <a-tag size="small" color="green">
                  {{ result.文档标题 }}
                </a-tag>
                <span class="meta-text">
                  文档ID: {{ result.文档ID }}
                </span>
              </a-space>
            </div>
          </div>

          <!-- 展开详情 -->
          <a-collapse ghost>
            <a-collapse-panel key="detail" header="查看详情">
              <a-descriptions size="small" :column="2">
                <a-descriptions-item label="文档路径">
                  {{ result.文档路径 || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="文档大小">
                  {{ result.文档大小 || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ result.创建时间 || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="更新时间">
                  {{ result.更新时间 || '未知' }}
                </a-descriptions-item>
                <a-descriptions-item label="向量维度" :span="2">
                  {{ result.向量维度 || '未知' }}
                </a-descriptions-item>
              </a-descriptions>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="hasTestedOnce" class="empty-results">
      <a-empty description="未找到相关结果">
        <template #image>
          <SearchOutlined style="font-size: 48px; color: #d9d9d9;" />
        </template>
      </a-empty>
    </div>

    <!-- 测试历史 -->
    <div v-if="testHistory.length > 0" class="test-history">
      <a-collapse ghost>
        <a-collapse-panel key="history" header="测试历史">
          <div class="history-list">
            <div 
              v-for="(history, index) in testHistory" 
              :key="index"
              class="history-item"
              @click="loadHistoryTest(history)"
            >
              <div class="history-query">{{ history.查询内容 }}</div>
              <div class="history-meta">
                <a-space size="small">
                  <span>{{ history.结果数量 }} 条结果</span>
                  <span>{{ history.测试时间 }}</span>
                </a-space>
              </div>
            </div>
          </div>
        </a-collapse-panel>
      </a-collapse>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { SearchOutlined, ClearOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import knowledgeBaseService from '@/services/knowledgeBaseService'

// Props
const props = defineProps({
  knowledgeBaseIds: {
    type: Array,
    default: () => []
  },
  retrievalConfig: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['test-result'])

// 测试状态
const testing = ref(false)
const hasTestedOnce = ref(false)
const testQuery = ref('')

// 测试参数
const testParams = ref({
  最大检索数量: 5,
  相似度阈值: 0.7,
  检索策略: 'vector'
})

// 测试结果
const testResults = ref([])
const testHistory = ref([])

// 计算属性
const averageScore = computed(() => {
  if (testResults.value.length === 0) return 0
  const sum = testResults.value.reduce((acc, result) => acc + result.相似度分数, 0)
  return sum / testResults.value.length
})

// 监听配置变化，同步测试参数
watch(() => props.retrievalConfig, (newConfig) => {
  if (newConfig) {
    testParams.value = { ...testParams.value, ...newConfig }
  }
}, { immediate: true, deep: true })

// 获取分数颜色
const getScoreColor = (score) => {
  if (score >= 0.8) return '#52c41a'
  if (score >= 0.6) return '#faad14'
  return '#ff4d4f'
}

// 运行测试
const runTest = async () => {
  if (!testQuery.value.trim()) {
    message.warning('请输入测试查询')
    return
  }

  if (props.knowledgeBaseIds.length === 0) {
    message.warning('请先选择知识库')
    return
  }

  testing.value = true
  hasTestedOnce.value = true

  try {
    // 验证必需参数
    if (!props.knowledgeBaseIds || props.knowledgeBaseIds.length === 0) {
      message.warning('请先选择知识库')
      return
    }

    if (!testQuery.value || testQuery.value.trim().length === 0) {
      message.warning('请输入测试查询内容')
      return
    }

    // 构建检索配置参数（使用实时页面参数）
    const 检索配置参数 = {
      检索策略: testParams.value.检索策略 || props.retrievalConfig?.检索策略 || 'vector',
      嵌入模型: props.retrievalConfig?.嵌入模型 || props.retrievalConfig?.嵌入模型名称 || 'text-embedding-v4',
      相似度阈值: testParams.value.相似度阈值 !== undefined ? testParams.value.相似度阈值 : (props.retrievalConfig?.相似度阈值 || 0.7),
      最大检索数量: testParams.value.最大检索数量 || props.retrievalConfig?.最大检索数量 || 10,
      分块大小: testParams.value.分块大小 || props.retrievalConfig?.分块大小 || 1000,
      分块重叠: testParams.value.分块重叠 || props.retrievalConfig?.分块重叠 || 200,
      分块策略: testParams.value.分块策略 || props.retrievalConfig?.分块策略 || 'recursive'
    }

    // 构建查询优化配置（如果启用）
    let 查询优化配置 = null
    if (props.retrievalConfig?.启用查询优化) {
      查询优化配置 = {
        启用查询优化: true,
        优化模型: props.retrievalConfig.查询优化模型,
        优化提示词: props.retrievalConfig.查询优化提示词
      }
    }

    console.log('📊 RAG测试面板 - 使用实时参数进行检索测试:', {
      知识库列表: props.knowledgeBaseIds,
      检索配置参数,
      查询优化配置
    })

    // 使用新版本接口进行多知识库检索测试
    const response = await knowledgeBaseService.testRetrievalNew(
      props.knowledgeBaseIds, // 知识库ID列表
      testQuery.value.trim(), // 测试查询
      检索配置参数, // 检索配置
      查询优化配置, // 查询优化配置
      'standard' // 测试模式
    )

    if (response.success) {
      testResults.value = response.data.检索结果 || []
      const 总结果数量 = response.data.总结果数量 || testResults.value.length

      // 添加到测试历史
      testHistory.value.unshift({
        查询内容: testQuery.value,
        结果数量: 总结果数量,
        测试时间: new Date().toLocaleString('zh-CN'),
        参数: { ...testParams.value },
        结果: testResults.value,
        知识库详情: response.data.知识库检索详情 || []
      })

      // 限制历史记录数量
      if (testHistory.value.length > 10) {
        testHistory.value = testHistory.value.slice(0, 10)
      }

      console.log('✅ RAG测试面板 - 检索测试成功（新版本）:', response.data)
    } else {
      console.warn('❌ RAG测试面板 - 检索测试失败:', response.error)
      testResults.value = []
    }

    emit('test-result', {
      query: testQuery.value,
      results: testResults.value,
      params: testParams.value
    })

    message.success(`检索完成，找到 ${testResults.value.length} 条相关结果`)
  } catch (error) {
    console.error('RAG测试失败:', error)
    message.error('测试失败，请重试')
  } finally {
    testing.value = false
  }
}

// 清空结果
const clearResults = () => {
  testResults.value = []
  hasTestedOnce.value = false
}

// 导出结果
const exportResults = () => {
  const data = {
    查询: testQuery.value,
    参数: testParams.value,
    结果: testResults.value,
    导出时间: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `rag_test_results_${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('结果已导出')
}

// 加载历史测试
const loadHistoryTest = (history) => {
  testQuery.value = history.查询内容
  testParams.value = { ...history.参数 }
  testResults.value = [...history.结果]
  hasTestedOnce.value = true
  message.info('已加载历史测试')
}
</script>

<style scoped>
.rag-test-panel {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
}

.test-header {
  margin-bottom: 16px;
}

.test-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.test-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 12px;
}

.test-form {
  margin-bottom: 24px;
}

.test-results {
  margin-top: 24px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.results-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.result-stats {
  font-size: 12px;
  color: #8c8c8c;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-index {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.result-score {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.score-text {
  font-size: 12px;
  font-weight: 500;
  color: #262626;
}

.result-content {
  margin-bottom: 12px;
}

.result-text {
  color: #262626;
  line-height: 1.6;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.result-meta {
  font-size: 12px;
}

.meta-text {
  color: #8c8c8c;
}

.empty-results {
  text-align: center;
  padding: 40px 20px;
}

.test-history {
  margin-top: 24px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  padding: 8px 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.history-query {
  font-size: 13px;
  color: #262626;
  margin-bottom: 4px;
  font-weight: 500;
}

.history-meta {
  font-size: 11px;
  color: #8c8c8c;
}

:deep(.ant-collapse-ghost .ant-collapse-item) {
  border-bottom: none;
}

:deep(.ant-collapse-ghost .ant-collapse-content) {
  background: transparent;
}

:deep(.ant-progress-inner) {
  background: #f5f5f5;
}
</style>
