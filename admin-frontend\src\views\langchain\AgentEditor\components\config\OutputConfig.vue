<template>
  <div class="output-config">
    <div class="section-header">
      <h2><CodeOutlined /> 结构化输出配置</h2>
      <p>配置智能体的输出格式和结构</p>
    </div>
    
    <a-form layout="vertical" class="config-form">
      <a-card title="输出格式设置" class="config-card">
        <!-- 输出格式选择 -->
        <a-form-item label="输出格式">
          <a-radio-group v-model:value="localForm.输出格式" @change="handleOutputModeChange">
            <a-radio value="text">纯文本</a-radio>
            <a-radio value="json">JSON格式</a-radio>
            <a-radio value="structured">结构化输出</a-radio>
            <a-radio value="pydantic">Pydantic模型</a-radio>
          </a-radio-group>
          <div class="mode-desc">
            {{ getModeDesc(localForm.输出格式) }}
          </div>
        </a-form-item>

        <!-- 结构化输出配置 -->
        <div v-if="['json', 'structured', 'pydantic'].includes(localForm.输出格式)" class="structured-output">
          <!-- JSON Schema编辑器 -->
          <a-form-item 
            label="JSON Schema" 
            :validate-status="errors.自定义回复格式 ? 'error' : ''"
            :help="errors.自定义回复格式"
          >
            <div class="schema-editor">
              <!-- 编辑器工具栏 -->
              <div class="editor-toolbar">
                <a-space>
                  <a-button size="small" @click="showSchemaBuilder = true">
                    <BuildOutlined /> 可视化构建
                  </a-button>
                  <a-button size="small" @click="loadTemplate">
                    <FileAddOutlined /> 加载模板
                  </a-button>
                  <a-button size="small" @click="validateSchema">
                    <CheckCircleOutlined /> 验证格式
                  </a-button>
                  <a-button size="small" @click="formatSchema">
                    <FormatPainterOutlined /> 格式化
                  </a-button>
                </a-space>
              </div>

              <!-- JSON编辑器 -->
              <a-textarea
                v-model:value="schemaText"
                placeholder="请输入JSON Schema或使用可视化构建器"
                :rows="12"
                class="schema-textarea"
                @blur="handleSchemaChange"
              />

              <!-- Schema统计 -->
              <div class="schema-stats">
                <a-space>
                  <span>字段数: {{ schemaStats.字段数 }}</span>
                  <span>嵌套层级: {{ schemaStats.嵌套层级 }}</span>
                  <a-tag v-if="schemaStats.是否有效" color="green">格式正确</a-tag>
                  <a-tag v-else color="red">格式错误</a-tag>
                </a-space>
              </div>
            </div>
          </a-form-item>

          <!-- Schema预览 -->
          <a-form-item label="输出预览">
            <div class="schema-preview">
              <a-tabs type="card">
                <a-tab-pane key="example" tab="示例输出">
                  <pre class="preview-code">{{ schemaExample }}</pre>
                </a-tab-pane>
                <a-tab-pane key="structure" tab="结构说明">
                  <div class="structure-info">
                    <SchemaStructureView :schema="parsedSchema" />
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </a-form-item>
        </div>

        <!-- 输出格式选项 -->
        <a-form-item label="输出格式选项">
          <a-checkbox-group v-model:value="outputOptions" @change="handleFormChange">
            <a-checkbox value="include_metadata">包含元数据</a-checkbox>
            <a-checkbox value="include_confidence">包含置信度</a-checkbox>
            <a-checkbox value="include_reasoning">包含推理过程</a-checkbox>
            <a-checkbox value="include_sources">包含信息来源</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <!-- 输出限制 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="最大输出长度">
              <a-input-number
                v-model:value="localForm.最大输出长度"
                :min="100"
                :max="10000"
                placeholder="字符数限制"
                @change="handleFormChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="输出语言">
              <a-select
                v-model:value="localForm.输出语言"
                placeholder="选择输出语言"
                @change="handleFormChange"
              >
                <a-select-option value="zh-CN">中文</a-select-option>
                <a-select-option value="en-US">英文</a-select-option>
                <a-select-option value="auto">自动检测</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </a-form>

    <!-- Schema可视化构建器弹窗 -->
    <a-modal
      v-model:open="showSchemaBuilder"
      title="JSON Schema 可视化构建器"
      width="1000px"
      :footer="null"
    >
      <SchemaBuilder
        :initial-schema="parsedSchema"
        @schema-change="handleBuilderSchemaChange"
        @close="showSchemaBuilder = false"
      />
    </a-modal>

    <!-- 模板选择弹窗 -->
    <a-modal
      v-model:open="showTemplateModal"
      title="选择Schema模板"
      width="800px"
      :footer="null"
    >
      <div class="template-grid">
        <div 
          v-for="template in schemaTemplates" 
          :key="template.name"
          class="template-card"
          @click="applyTemplate(template)"
        >
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
          <pre class="template-preview">{{ JSON.stringify(template.schema, null, 2) }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { CodeOutlined, BuildOutlined, FileAddOutlined, CheckCircleOutlined, FormatPainterOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import SchemaBuilder from './SchemaBuilder.vue'
import SchemaStructureView from './SchemaStructureView.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'change'])

// 本地状态
const showSchemaBuilder = ref(false)
const showTemplateModal = ref(false)
const outputOptions = ref([])

// 本地表单数据
const localForm = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Schema文本
const schemaText = computed({
  get: () => {
    if (!localForm.value.自定义回复格式) return ''
    return typeof localForm.value.自定义回复格式 === 'string'
      ? localForm.value.自定义回复格式
      : JSON.stringify(localForm.value.自定义回复格式, null, 2)
  },
  set: (value) => {
    try {
      localForm.value.自定义回复格式 = value ? JSON.parse(value) : null
    } catch (error) {
      localForm.value.自定义回复格式 = value
    }
  }
})

// 解析后的Schema
const parsedSchema = computed(() => {
  try {
    return typeof localForm.value.自定义回复格式 === 'string'
      ? JSON.parse(localForm.value.自定义回复格式)
      : localForm.value.自定义回复格式
  } catch (error) {
    return null
  }
})

// Schema统计信息
const schemaStats = computed(() => {
  const schema = parsedSchema.value
  if (!schema) {
    return { 字段数: 0, 嵌套层级: 0, 是否有效: false }
  }

  const countFields = (obj, level = 0) => {
    let count = 0
    let maxLevel = level
    
    if (obj && typeof obj === 'object') {
      if (obj.properties) {
        count += Object.keys(obj.properties).length
        Object.values(obj.properties).forEach(prop => {
          const result = countFields(prop, level + 1)
          count += result.count
          maxLevel = Math.max(maxLevel, result.maxLevel)
        })
      }
    }
    
    return { count, maxLevel }
  }

  const result = countFields(schema)
  return {
    字段数: result.count,
    嵌套层级: result.maxLevel,
    是否有效: true
  }
})

// Schema示例
const schemaExample = computed(() => {
  const schema = parsedSchema.value
  if (!schema) return '请先配置JSON Schema'
  
  // 生成示例数据
  const generateExample = (schema) => {
    if (!schema || typeof schema !== 'object') return null
    
    if (schema.type === 'object' && schema.properties) {
      const example = {}
      Object.entries(schema.properties).forEach(([key, prop]) => {
        example[key] = generateExample(prop)
      })
      return example
    }
    
    if (schema.type === 'array' && schema.items) {
      return [generateExample(schema.items)]
    }
    
    switch (schema.type) {
      case 'string': return schema.example || '示例文本'
      case 'number': return schema.example || 123
      case 'integer': return schema.example || 42
      case 'boolean': return schema.example || true
      default: return null
    }
  }
  
  const example = generateExample(schema)
  return JSON.stringify(example, null, 2)
})

// Schema模板
const schemaTemplates = ref([
  {
    name: '简单对象',
    description: '包含基本字段的简单对象',
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string', description: '标题' },
        content: { type: 'string', description: '内容' },
        score: { type: 'number', description: '评分' }
      },
      required: ['title', 'content']
    }
  },
  {
    name: '列表结构',
    description: '包含数组的复杂结构',
    schema: {
      type: 'object',
      properties: {
        items: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              value: { type: 'number' }
            }
          }
        },
        total: { type: 'number' }
      }
    }
  }
])

// 获取模式描述
const getModeDesc = (mode) => {
  const descriptions = {
    'text': '智能体输出自然语言文本',
    'json': '智能体输出标准JSON格式，支持复杂嵌套结构',
    'structured': '智能体输出预定义结构的数据',
    'pydantic': '智能体输出基于Pydantic模型的严格类型化数据'
  }
  return descriptions[mode] || ''
}

// 处理输出格式变化
const handleOutputModeChange = () => {
  if (localForm.value.输出格式 === 'text') {
    localForm.value.自定义回复格式 = null
  }
  handleFormChange()
}

// 处理Schema变化
const handleSchemaChange = () => {
  validateSchema()
  handleFormChange()
}

// 验证Schema
const validateSchema = () => {
  try {
    if (schemaText.value) {
      JSON.parse(schemaText.value)
      message.success('Schema格式正确')
      emit('validate', { 自定义回复格式: null })
    }
  } catch (error) {
    message.error('Schema格式错误: ' + error.message)
    emit('validate', { 自定义回复格式: 'JSON格式不正确' })
  }
}

// 格式化Schema
const formatSchema = () => {
  try {
    if (schemaText.value) {
      const parsed = JSON.parse(schemaText.value)
      schemaText.value = JSON.stringify(parsed, null, 2)
    }
  } catch (error) {
    message.error('无法格式化：JSON格式不正确')
  }
}

// 加载模板
const loadTemplate = () => {
  showTemplateModal.value = true
}

// 应用模板
const applyTemplate = (template) => {
  localForm.value.自定义回复格式 = template.schema
  showTemplateModal.value = false
  handleFormChange()
}

// 处理构建器Schema变化
const handleBuilderSchemaChange = (schema) => {
  localForm.value.自定义回复格式 = schema
  handleFormChange()
}

// 处理表单变化
const handleFormChange = () => {
  emit('change', localForm.value)
}

// 初始化默认值
if (!localForm.value.输出格式) localForm.value.输出格式 = 'text'
if (!localForm.value.最大输出长度) localForm.value.最大输出长度 = 2000
if (!localForm.value.输出语言) localForm.value.输出语言 = 'zh-CN'
</script>

<style scoped>
.output-config {
  padding: 16px;
}

.section-header {
  margin-bottom: 24px;
}

.section-header h2 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.config-form {
  max-width: 800px;
}

.config-card {
  margin-bottom: 16px;
}

.mode-desc {
  margin-top: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

.structured-output {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.schema-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.schema-textarea {
  border: none;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.schema-textarea:focus {
  box-shadow: none;
}

.schema-stats {
  padding: 8px 12px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
}

.schema-preview {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.preview-code {
  background: #f5f5f5;
  padding: 16px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
  overflow-x: auto;
}

.structure-info {
  padding: 16px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.template-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.template-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.template-desc {
  color: #8c8c8c;
  font-size: 12px;
  margin-bottom: 12px;
}

.template-preview {
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  font-size: 11px;
  line-height: 1.4;
  max-height: 120px;
  overflow-y: auto;
}
</style>
